"""
PDF Job Management API Handler

This module handles asynchronous PDF processing jobs to work around
API Gateway's 29-second timeout limitation.

Endpoints:
- POST /pdf-jobs - Submit a PDF for asynchronous processing
- GET /pdf-jobs/{jobId} - Get job status and results
- GET /pdf-jobs - List user's PDF jobs
"""

import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import boto3
from botocore.exceptions import ClientError

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.logging import correlation_paths
from aws_lambda_powertools.metrics import MetricUnit
from aws_lambda_powertools.event_handler import APIGatewayRestResolver
from aws_lambda_powertools.event_handler.exceptions import (
    BadRequestError,
    NotFoundError,
    InternalServerError,
    UnauthorizedError
)

from shared.api.auth_dependencies import get_user_from_event
from shared.api.responses import create_response, CORSHandler

# Initialize AWS services
logger = Logger()
tracer = Tracer()
metrics = Metrics()
app = APIGatewayRestResolver()

# Environment variables
import os
PDF_JOB_TABLE = os.environ.get('PDF_JOB_TABLE')
LAMBDA_FUNCTION_NAME = os.environ.get('PDF_EXTRACTOR_FUNCTION_NAME')

# AWS clients
dynamodb = boto3.resource('dynamodb')
lambda_client = boto3.client('lambda')

# Job statuses
class JobStatus:
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


@app.post("/pdf-jobs")
@tracer.capture_method
def submit_pdf_job():
    """Submit a PDF file for asynchronous processing"""
    try:
        # Get authenticated user
        user = get_user_from_event(app.current_event)
        if not user:
            raise UnauthorizedError("Authentication required")

        # Parse multipart form data
        content_type = app.current_event.get('headers', {}).get('content-type', '')
        if 'multipart/form-data' not in content_type:
            raise BadRequestError("Content-Type must be multipart/form-data")

        # Extract file from event (this is simplified - in practice you'd need proper multipart parsing)
        body = app.current_event.get('body', '')
        if not body:
            raise BadRequestError("No file provided")

        # Generate job ID
        job_id = str(uuid.uuid4())
        
        # Create job record
        job_table = dynamodb.Table(PDF_JOB_TABLE)
        current_time = datetime.utcnow()
        ttl_time = current_time + timedelta(days=7)  # Jobs expire after 7 days
        
        job_item = {
            'jobId': job_id,
            'userId': user['user_id'],
            'status': JobStatus.PENDING,
            'createdAt': current_time.isoformat(),
            'updatedAt': current_time.isoformat(),
            'ttl': int(ttl_time.timestamp()),
            'fileName': app.current_event.get('headers', {}).get('x-file-name', 'unknown.pdf'),
            'fileSize': len(body),
        }
        
        job_table.put_item(Item=job_item)
        
        # Invoke PDF extractor Lambda asynchronously
        payload = {
            'jobId': job_id,
            'userId': user['user_id'],
            'body': body,
            'headers': app.current_event.get('headers', {}),
            'isAsyncJob': True
        }
        
        lambda_client.invoke(
            FunctionName=LAMBDA_FUNCTION_NAME,
            InvocationType='Event',  # Asynchronous invocation
            Payload=json.dumps(payload)
        )
        
        # Update job status to processing
        job_table.update_item(
            Key={'jobId': job_id},
            UpdateExpression='SET #status = :status, updatedAt = :updated',
            ExpressionAttributeNames={'#status': 'status'},
            ExpressionAttributeValues={
                ':status': JobStatus.PROCESSING,
                ':updated': datetime.utcnow().isoformat()
            }
        )
        
        logger.info(f"PDF job submitted successfully", extra={"job_id": job_id, "user_id": user['user_id']})
        metrics.add_metric(name="PDFJobSubmitted", unit=MetricUnit.Count, value=1)
        
        return create_response(
            data={
                'jobId': job_id,
                'status': JobStatus.PROCESSING,
                'message': 'PDF processing job submitted successfully'
            },
            message="Job submitted successfully"
        )
        
    except Exception as e:
        logger.error(f"Error submitting PDF job: {str(e)}")
        metrics.add_metric(name="PDFJobSubmissionError", unit=MetricUnit.Count, value=1)
        raise InternalServerError(f"Failed to submit PDF job: {str(e)}")


@app.get("/pdf-jobs/<job_id>")
@tracer.capture_method
def get_pdf_job_status(job_id: str):
    """Get the status and results of a PDF processing job"""
    try:
        # Get authenticated user
        user = get_user_from_event(app.current_event)
        if not user:
            raise UnauthorizedError("Authentication required")

        # Get job from database
        job_table = dynamodb.Table(PDF_JOB_TABLE)
        
        try:
            response = job_table.get_item(Key={'jobId': job_id})
            if 'Item' not in response:
                raise NotFoundError(f"Job {job_id} not found")
                
            job = response['Item']
            
            # Check if user owns this job
            if job['userId'] != user['user_id']:
                raise UnauthorizedError("Access denied to this job")
                
        except ClientError as e:
            logger.error(f"Error retrieving job {job_id}: {str(e)}")
            raise InternalServerError("Failed to retrieve job status")
        
        logger.info(f"Retrieved job status", extra={"job_id": job_id, "status": job['status']})
        
        return create_response(
            data=job,
            message="Job status retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting job status: {str(e)}")
        raise InternalServerError(f"Failed to get job status: {str(e)}")


@app.get("/pdf-jobs")
@tracer.capture_method
def list_pdf_jobs():
    """List all PDF processing jobs for the authenticated user"""
    try:
        # Get authenticated user
        user = get_user_from_event(app.current_event)
        if not user:
            raise UnauthorizedError("Authentication required")

        # Query parameters
        limit = int(app.current_event.get('queryStringParameters', {}).get('limit', 50))
        limit = min(limit, 100)  # Cap at 100
        
        # Query jobs by user
        job_table = dynamodb.Table(PDF_JOB_TABLE)
        
        try:
            response = job_table.query(
                IndexName='UserIdIndex',
                KeyConditionExpression='userId = :userId',
                ExpressionAttributeValues={':userId': user['user_id']},
                ScanIndexForward=False,  # Most recent first
                Limit=limit
            )
            
            jobs = response.get('Items', [])
            
        except ClientError as e:
            logger.error(f"Error listing jobs for user {user['user_id']}: {str(e)}")
            raise InternalServerError("Failed to list jobs")
        
        logger.info(f"Listed {len(jobs)} jobs for user", extra={"user_id": user['user_id']})
        
        return create_response(
            data=jobs,
            message=f"Retrieved {len(jobs)} jobs"
        )
        
    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        raise InternalServerError(f"Failed to list jobs: {str(e)}")


@logger.inject_lambda_context(correlation_id_path=correlation_paths.API_GATEWAY_REST)
@tracer.capture_lambda_handler
@metrics.log_metrics(capture_cold_start_metric=True)
def handler(event, context):
    """Main Lambda handler for PDF job management"""
    try:
        # Handle CORS preflight requests
        if CORSHandler.is_options_request(event):
            return CORSHandler.handle_options()

        logger.info("PDF Jobs API request received", extra={"event": event})
        return app.resolve(event, context)
    except Exception as e:
        logger.error(f"Unhandled error in PDF Jobs API: {str(e)}")
        return create_response(
            data=None,
            message=f"Internal server error: {str(e)}",
            success=False,
            status_code=500
        )
