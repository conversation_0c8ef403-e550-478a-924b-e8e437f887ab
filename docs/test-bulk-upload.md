# Bulk Upload Testing Guide

## Test Steps

1. **Navigate to Bulk Upload Page**
   - Go to http://localhost:3000/funds
   - Click the "Bulk Upload" button in the header
   - Should navigate to http://localhost:3000/funds/bulk-upload

2. **Test File Upload Interface**
   - Verify the drag-and-drop area is visible
   - Check that instructions and tips are displayed
   - Ensure the file input accepts only PDF files

3. **Test File Selection**
   - Try uploading a PDF file (or create a test PDF)
   - Verify file validation (size, type)
   - Check that selected files appear in the list

4. **Test Upload Process**
   - Click "Upload Files" button
   - Verify progress indicators work
   - Check that mock API is called (since USE_AWS_API=false)

5. **Test Results Display**
   - Verify upload results are shown
   - Check status indicators (success/error/warning)
   - Test "View" and "Edit" buttons for successful uploads

6. **Test Navigation**
   - Click "Edit" on a successful result
   - Should navigate to fund edit page
   - Click "View" on a successful result
   - Should navigate to fund detail page

## Expected Behavior

- Mock data should be used (USE_AWS_API=false, ENABLE_MOCK_FALLBACK=true)
- Upload should simulate processing time (1-4 seconds)
- 80% success rate for mock uploads
- Successful uploads should generate fund data
- Error uploads should show appropriate error messages
- Navigation to fund edit/view should work

## Mock Data Features

- Generates realistic fund data from PDF file names
- Simulates processing time
- Random success/error/warning results
- Includes validation warnings for some uploads
- Creates fund IDs with "pdf-" prefix for testing
