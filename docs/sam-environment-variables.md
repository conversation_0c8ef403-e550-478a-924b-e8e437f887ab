# SAM Deploy Environment Variables

This document details the environment variables that are automatically set during the SAM deployment process for the FundFlow application.

## Overview

Environment variables in the FundFlow application are configured through the SAM template (`template.yaml`) and can be customized via:

- Parameter overrides in `samconfig.toml`
- Environment-specific parameter files (`dev.json`, `staging.json`, `prod.json`)
- Command-line parameter overrides

## Global Environment Variables

These variables are applied to all Lambda functions via the `Globals.Function.Environment.Variables` section:

| Variable                       | Value                  | Description                              |
| ------------------------------ | ---------------------- | ---------------------------------------- |
| `POWERTOOLS_SERVICE_NAME`      | `fundflow`             | AWS Lambda Powertools service identifier |
| `POWERTOOLS_METRICS_NAMESPACE` | `fundflow`             | CloudWatch metrics namespace             |
| `LOG_LEVEL`                    | `INFO` (default)       | Logging level (DEBUG/INFO/WARNING/ERROR) |
| `ENVIRONMENT`                  | `dev`/`staging`/`prod` | Current deployment environment           |
| `COGNITO_USER_POOL_ID`         | Parameter reference    | Cognito User Pool ID for authentication  |
| `COGNITO_APP_CLIENT_ID`        | Parameter reference    | Cognito App Client ID for authentication |

## Function-Specific Environment Variables

### Health Check Function (`HealthCheckFunction`)

| Variable                       | Value                 | Description                    |
| ------------------------------ | --------------------- | ------------------------------ |
| `ENVIRONMENT`                  | Environment parameter | Deployment environment         |
| `API_VERSION`                  | `"1.0.0"`             | API version identifier         |
| `LOG_LEVEL`                    | Parameter value       | Function-specific log level    |
| `POWERTOOLS_SERVICE_NAME`      | `fundflow-health`     | Service name for this function |
| `POWERTOOLS_METRICS_NAMESPACE` | `FundFlow`            | Metrics namespace              |

### Funds API Function (`FundsAPIFunction`)

| Variable                       | Value                    | Description                       |
| ------------------------------ | ------------------------ | --------------------------------- |
| `ENVIRONMENT`                  | Environment parameter    | Deployment environment            |
| `FUND_TABLE`                   | DynamoDB table reference | Fund data table name              |
| `HISTORICAL_PRICES_TABLE`      | DynamoDB table reference | Historical prices table name      |
| `USER_TABLE`                   | DynamoDB table reference | User data table name              |
| `USER_POOL_ID`                 | Cognito reference        | User Pool ID (conditional)        |
| `USER_POOL_CLIENT_ID`          | Cognito reference        | User Pool Client ID (conditional) |
| `LOG_LEVEL`                    | Parameter value          | Function-specific log level       |
| `POWERTOOLS_SERVICE_NAME`      | `fundflow-funds-api`     | Service name for this function    |
| `POWERTOOLS_METRICS_NAMESPACE` | `FundFlow`               | Metrics namespace                 |

### Users API Function (`UsersAPIFunction`)

| Variable                       | Value                    | Description                       |
| ------------------------------ | ------------------------ | --------------------------------- |
| `ENVIRONMENT`                  | Environment parameter    | Deployment environment            |
| `USER_TABLE`                   | DynamoDB table reference | User data table name              |
| `USER_POOL_ID`                 | Cognito reference        | User Pool ID (conditional)        |
| `USER_POOL_CLIENT_ID`          | Cognito reference        | User Pool Client ID (conditional) |
| `LOG_LEVEL`                    | Parameter value          | Function-specific log level       |
| `POWERTOOLS_SERVICE_NAME`      | `fundflow-users-api`     | Service name for this function    |
| `POWERTOOLS_METRICS_NAMESPACE` | `FundFlow`               | Metrics namespace                 |

### Portfolios API Function (`PortfoliosAPIFunction`)

| Variable                       | Value                    | Description                       |
| ------------------------------ | ------------------------ | --------------------------------- |
| `ENVIRONMENT`                  | Environment parameter    | Deployment environment            |
| `PORTFOLIO_TABLE`              | DynamoDB table reference | Portfolio data table name         |
| `USER_TABLE`                   | DynamoDB table reference | User data table name              |
| `USER_POOL_ID`                 | Cognito reference        | User Pool ID (conditional)        |
| `USER_POOL_CLIENT_ID`          | Cognito reference        | User Pool Client ID (conditional) |
| `LOG_LEVEL`                    | Parameter value          | Function-specific log level       |
| `POWERTOOLS_SERVICE_NAME`      | `fundflow-portfolios`    | Service name for this function    |
| `POWERTOOLS_METRICS_NAMESPACE` | `FundFlow`               | Metrics namespace                 |

### Fund Extractor Function (`FundExtractorFunction`)

| Variable                       | Value                    | Description                     |
| ------------------------------ | ------------------------ | ------------------------------- |
| `ENVIRONMENT`                  | Environment parameter    | Deployment environment          |
| `FUND_TABLE`                   | DynamoDB table reference | Fund data table name            |
| `HISTORICAL_PRICES_TABLE`      | DynamoDB table reference | Historical prices table name    |
| `USER_TABLE`                   | DynamoDB table reference | User data table name            |
| `PDF_JOB_TABLE`                | DynamoDB table reference | PDF processing jobs table name  |
| `COGNITO_USER_POOL_ID`         | Parameter reference      | Cognito User Pool ID            |
| `COGNITO_APP_CLIENT_ID`        | Parameter reference      | Cognito App Client ID           |
| `LOG_LEVEL`                    | Parameter value          | Function-specific log level     |
| `POWERTOOLS_SERVICE_NAME`      | `fundflow-pdf-extractor` | Service name for this function  |
| `POWERTOOLS_METRICS_NAMESPACE` | `FundFlow`               | Metrics namespace               |
| `OPENROUTER_API_KEY`           | Parameter reference      | API key for AI service (NoEcho) |

**Note**: This function has increased memory (1024 MB) and timeout (300 seconds) for AI processing.

### Jobs Function (`JobsFunction`)

| Variable                       | Value                    | Description                    |
| ------------------------------ | ------------------------ | ------------------------------ |
| `ENVIRONMENT`                  | Environment parameter    | Deployment environment         |
| `PDF_JOB_TABLE`                | DynamoDB table reference | PDF processing jobs table name |
| `PDF_EXTRACTOR_FUNCTION_NAME`  | Function reference       | Fund extractor function name   |
| `LOG_LEVEL`                    | Parameter value          | Function-specific log level    |
| `POWERTOOLS_SERVICE_NAME`      | `fundflow-pdf-jobs`      | Service name for this function |
| `POWERTOOLS_METRICS_NAMESPACE` | `FundFlow`               | Metrics namespace              |

## Parameter Configuration

### Default Values (from template.yaml)

| Parameter          | Default | Description                      |
| ------------------ | ------- | -------------------------------- |
| `Environment`      | `dev`   | Deployment environment           |
| `LogLevel`         | `INFO`  | Default logging level            |
| `LambdaMemorySize` | `512`   | Default Lambda memory (MB)       |
| `LambdaTimeout`    | `30`    | Default Lambda timeout (seconds) |

### Environment-Specific Overrides

#### Development (dev.json / samconfig.toml [dev])

```json
{
  "Environment": "dev",
  "LogLevel": "DEBUG",
  "ExistingUserPoolId": "ap-northeast-1_H2kKHGUAT",
  "ExistingUserPoolClientId": "2jh76f894g6lv9vrus4qbb9hu7"
}
```

#### Staging

```json
{
  "Environment": "staging",
  "LogLevel": "INFO",
  "LambdaMemorySize": "1024",
  "EnableDetailedMonitoring": "true"
}
```

#### Production

```json
{
  "Environment": "prod",
  "LogLevel": "WARNING",
  "LambdaMemorySize": "1024",
  "LambdaTimeout": "60",
  "EnableDetailedMonitoring": "true"
}
```

## Sensitive Variables

The following environment variables contain sensitive information and are marked with `NoEcho: true`:

- **`OPENROUTER_API_KEY`**: API key for AI-powered PDF fund extraction service

## Resource References

Many environment variables reference AWS resources created during deployment:

- **Table Names**: DynamoDB table names are auto-generated with stack name prefix
- **Function ARNs**: Lambda function ARNs are resolved by CloudFormation
- **Cognito Resources**: User Pool and Client IDs can be existing or newly created

## Usage Examples

### Accessing in Lambda Code

```python
import os

# Get environment variables
environment = os.environ.get('ENVIRONMENT')
fund_table = os.environ.get('FUND_TABLE')
log_level = os.environ.get('LOG_LEVEL', 'INFO')
openrouter_key = os.environ.get('OPENROUTER_API_KEY')
```

### Setting via Command Line

```bash
# Deploy with custom parameters
sam deploy --parameter-overrides \
  Environment=dev \
  LogLevel=DEBUG \
  ExistingUserPoolId=your-pool-id \
  OpenRouterAPIKey=your-api-key
```

### Setting via Environment File

For development, you can set the OpenRouter API key in your shell:

```bash
export OPENROUTER_API_KEY="your-api-key-here"
sam deploy --config-env dev
```

## Security Notes

1. **Sensitive Values**: Use `NoEcho: true` for sensitive parameters
2. **API Keys**: Store API keys in environment variables, not in configuration files
3. **IAM Permissions**: Lambda functions automatically get appropriate DynamoDB permissions via SAM policies
4. **Cognito Integration**: User Pool IDs are safely referenced, not hardcoded

## Troubleshooting

### Common Issues

1. **Missing API Key**: Ensure `OPENROUTER_API_KEY` is set when deploying
2. **Table Not Found**: Verify DynamoDB tables are created successfully
3. **Cognito Errors**: Check User Pool and Client IDs are correct for environment
4. **Permission Errors**: Ensure Lambda execution roles have necessary DynamoDB permissions

### Debugging

Check CloudWatch logs for environment variable values:

```python
import json
import os

def handler(event, context):
    # Log all environment variables (be careful with sensitive data)
    env_vars = {k: v for k, v in os.environ.items() if not k.endswith('_KEY')}
    print(f"Environment variables: {json.dumps(env_vars, indent=2)}")
```

## Related Documentation

- [SAM Template Reference](../template.yaml)
- [Parameter Configuration](../parameters/)
- [AWS Lambda Environment Variables](https://docs.aws.amazon.com/lambda/latest/dg/configuration-envvars.html)
- [AWS Lambda Powertools](https://docs.powertools.aws.dev/lambda/python/)
